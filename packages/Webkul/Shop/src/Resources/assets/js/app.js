/**
 * This will track all the images and fonts for publishing.
 */
import.meta.glob(["../images/**", "../fonts/**"]);

/**
 * Main vue bundler.
 */
import { createApp } from "vue/dist/vue.esm-bundler";

/**
 * Main root application registry.
 */
window.app = createApp({
    data() {
        return {};
    },

    mounted() {
        this.lazyImages();
        this.setupAosRefresh();
    },

    methods: {
        onSubmit() {},

        onInvalidSubmit() {},

        setupAosRefresh() {
            // Global method to refresh AOS animations
            window.refreshAOS = () => {
                if (window.Aos) {
                    window.Aos.refresh();
                }
            };
        },

        lazyImages() {
            var lazyImages = [].slice.call(document.querySelectorAll('img.lazy'));

            let lazyImageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        let lazyImage = entry.target;

                        lazyImage.src = lazyImage.dataset.src;

                        lazyImage.classList.remove('lazy');

                        lazyImageObserver.unobserve(lazyImage);
                    }
                });
            });

            lazyImages.forEach(function(lazyImage) {
                lazyImageObserver.observe(lazyImage);
            });
        },
    },
});

/**
 * Global plugins registration.
 */
import Axios from "./plugins/axios";
import Emitter from "./plugins/emitter";
import Shop from "./plugins/shop";
import VeeValidate from "./plugins/vee-validate";
import Flatpickr from "./plugins/flatpickr";
import ScrollyVideo from "./plugins/scrolly-video";
import Aos from "./plugins/aos";

[
    Axios,
    Emitter,
    Shop,
    VeeValidate,
    Flatpickr,
    ScrollyVideo,
    Aos,
].forEach((plugin) => app.use(plugin));

export default app;
