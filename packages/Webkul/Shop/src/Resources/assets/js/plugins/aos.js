import Aos from '/node_modules/aos/dist/aos.js';
import '/node_modules/aos/dist/aos.css';

export default {
    install: (app) => {
        window.Aos = Aos;
        Aos.init({
            // Global settings for AOS
            duration: 800,
            easing: 'ease-out',
            once: false, // Allow animations to trigger multiple times
            mirror: false, // Don't animate elements out while scrolling past them
            anchorPlacement: 'top-bottom', // Defines which position of the element regarding to window should trigger the animation
            disable: false, // Conditions when AOS should be disabled
            startEvent: 'DOMContentLoaded', // Name of the event dispatched on the document
            initClassName: 'aos-init', // Class applied after initialization
            animatedClassName: 'aos-animate', // Class applied on animation
            useClassNames: false, // If true, will add content of `data-aos` as classes on scroll
            disableMutationObserver: false, // Disables automatic mutations' detections
            debounceDelay: 50, // The delay on debounce used while resizing window
            throttleDelay: 99, // The delay on throttle used while scrolling the page
        });
    },
};
